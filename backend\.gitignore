# ========== Node.js ==========
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ========== 运行时文件 ==========
*.pid
*.seed
*.pid.lock

# ========== 覆盖率目录 ==========
lib-cov
coverage/
*.lcov
.nyc_output

# ========== 依赖目录 ==========
jspm_packages/

# ========== TypeScript ==========
*.tsbuildinfo

# ========== 可选的npm缓存目录 ==========
.npm

# ========== 可选的eslint缓存 ==========
.eslintcache

# ========== 可选的stylelint缓存 ==========
.stylelintcache

# ========== Microbundle缓存 ==========
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ========== 可选的REPL历史 ==========
.node_repl_history

# ========== 输出目录 ==========
dist/
build/

# ========== Grunt中间存储 ==========
.grunt

# ========== Bower依赖目录 ==========
bower_components

# ========== node-waf配置 ==========
.lock-wscript

# ========== 编译的二进制插件 ==========
build/Release

# ========== 依赖目录 ==========
node_modules/

# ========== 环境变量文件 ==========
# 排除所有环境变量文件（包含敏感信息）
.env
.env.*
.env.local
.env.development
.env.development.local
.env.production
.env.test.local
.env.production.local
.env.example
.env.development
.env.production
.env

# ========== 日志文件 ==========
logs/
*.log

# ========== 临时文件 ==========
tmp/
temp/

# ========== 操作系统生成的文件 ==========
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ========== IDE和编辑器 ==========
.vscode/
.idea/
*.swp
*.swo
*~

# ========== 音频文件和数据 ==========
/var/data/
/var/log/
audios/
*.mp3
*.wav
*.m4a
*.aac

# ========== 数据库文件 ==========
*.sqlite
*.db

# ========== 备份文件 ==========
*.bak
*.backup

# ========== PM2 ==========
.pm2/

# ========== 测试文件 ==========
test_*.js
*_test.js
performance_comparison_chart.js
quick_test.js
real_log_analysis.js
run_voice_grouping_tests.js

# ========== 开发日志和文档 ==========
本地开发日志/
*.txt
声音分组*.md

# ========== 其他 ==========
.cache/
.parcel-cache/
